import { useState, useCallback, useEffect } from 'react';
import { getCronJobHistory, type <PERSON>ronJobHistory } from '../api/index';
import { saveCronJobHistoryToCache, getCronJobHistoryFromCache } from '../utils/cache-utils';

interface CronJobHistoryState {
  history: CronJobHistory[];
  loading: boolean;
  page: number;
  pageSize: number;
  nameFilter?: string;
  successFilter?: boolean;
  newDataAvailable: boolean;
  hasMore: boolean;
  totalItems: number;
  totalPages: number;
}

interface UseCronJobHistoryProps {
  isCacheBust: boolean;
  setErrorWithScroll: (errorMessage: string, toastId?: string) => void;
}

export const useCronJobHistory = ({ isCacheBust, setErrorWithScroll }: UseCronJobHistoryProps) => {
  const cachedHistory = isCacheBust ? null : getCronJobHistoryFromCache();

  const [state, setState] = useState<CronJobHistoryState>({
    history: cachedHistory?.history || [],
    loading: !cachedHistory,
    page: 1,
    pageSize: parseInt(localStorage.getItem('cronHistoryPageSize') || '10', 10),
    newDataAvailable: false,
    hasMore: true,
    totalItems: 0,
    totalPages: 0,
    nameFilter: localStorage.getItem('cronHistoryJobFilter') === 'all' ? undefined : localStorage.getItem('cronHistoryJobFilter') || undefined,
    successFilter: (() => {
      const saved = localStorage.getItem('cronHistoryStatusFilter');
      if (!saved || saved === 'all') return undefined;
      return saved === 'success';
    })()
  });

  const fetchCronJobHistory = useCallback(async (forceFetch: boolean = false, append: boolean = false) => {
    const hasCache = getCronJobHistoryFromCache() !== null;
    const isBackgroundFetch = hasCache && !forceFetch && !state.loading;

    try {
      if (!hasCache && !forceFetch && !append) {
        setState((prev) => ({ ...prev, loading: true }));
      }

      const currentPage = append ? state.page + 1 : state.page;

      const historyResponse = await getCronJobHistory(
        state.nameFilter,
        currentPage,
        state.pageSize,
        state.successFilter
      );

      const cachedData = getCronJobHistoryFromCache();

      if (isBackgroundFetch && cachedData && !append) {
        const hasChanges = JSON.stringify(historyResponse) !== JSON.stringify(cachedData.history);
        if (hasChanges) {
          setTimeout(() => {
            setState(prev => ({ ...prev, newDataAvailable: true }));
          }, 100);

          if (!isCacheBust) {
            saveCronJobHistoryToCache({
              history: historyResponse,
              total: historyResponse.length,
              timestamp: Date.now()
            });
          }
        }
      } else if (!isBackgroundFetch || isCacheBust) {
        const newHistory = append ? [...state.history, ...historyResponse] : historyResponse;
        const hasMore = historyResponse.length === state.pageSize;

        // Calculate total items and pages based on current data
        // Since API doesn't return total count, we estimate based on current page and data
        const totalItems = append ? newHistory.length : historyResponse.length;
        const totalPages = Math.max(1, Math.ceil(totalItems / state.pageSize));

        setState(prev => ({
          ...prev,
          history: newHistory,
          page: currentPage,
          hasMore,
          totalItems,
          totalPages
        }));

        if (!append) {
          saveCronJobHistoryToCache({
            history: newHistory,
            total: newHistory.length,
            timestamp: Date.now()
          });
        }
      }
    } catch (error) {
      const err = error as Error;
      if (!isBackgroundFetch || isCacheBust) {
        setErrorWithScroll('Failed to fetch cron job history: ' + (err.message || 'Unknown error'), 'cron-job-history-error');
        throw err;
      }
    } finally {
      if (!isBackgroundFetch || isCacheBust) {
        setState(prev => ({ ...prev, loading: false }));
      }
    }

  }, [state.loading, setErrorWithScroll, isCacheBust, state.nameFilter, state.successFilter, state.page, state.pageSize]);

  // Auto-fetch when filters or page change
  useEffect(() => {
    if (state.loading) {
      fetchCronJobHistory(true);
    }
  }, [state.loading, fetchCronJobHistory]);

  // Initial fetch with saved filters
  useEffect(() => {
    fetchCronJobHistory(false);
  }, []);

  const applyNewData = useCallback(() => {
    if (state.newDataAvailable) {
      const historyCache = getCronJobHistoryFromCache();
      if (historyCache) {
        setState(prev => ({
          ...prev,
          history: historyCache.history,
          newDataAvailable: false
        }));
      }
    }
  }, [state.newDataAvailable]);

  const setNameFilter = (nameFilter?: string) => {
    const filterValue = nameFilter || 'all';
    localStorage.setItem('cronHistoryJobFilter', filterValue);
    setState(prev => ({
      ...prev,
      nameFilter,
      page: 1,
      history: [],
      hasMore: true,
      loading: true
    }));
  };

  const setSuccessFilter = (successFilter?: boolean) => {
    console.log('setSuccessFilter called with:', successFilter);
    const filterValue = successFilter === undefined ? 'all' : successFilter ? 'success' : 'failed';
    console.log('Saving to localStorage:', filterValue);
    localStorage.setItem('cronHistoryStatusFilter', filterValue);
    setState(prev => ({
      ...prev,
      successFilter,
      page: 1,
      history: [],
      hasMore: true,
      loading: true
    }));
  };

  const loadMore = () => {
    if (state.hasMore && !state.loading) {
      setState(prev => ({ ...prev, loading: true }));
    }
  };

  const resetFilters = () => {
    localStorage.setItem('cronHistoryJobFilter', 'all');
    localStorage.setItem('cronHistoryStatusFilter', 'all');
    setState(prev => ({
      ...prev,
      nameFilter: undefined,
      successFilter: undefined,
      page: 1,
      history: [],
      hasMore: true,
      loading: true
    }));
  };

  const setPageSize = (pageSize: number) => {
    localStorage.setItem('cronHistoryPageSize', pageSize.toString());
    setState(prev => ({
      ...prev,
      pageSize,
      page: 1,
      history: [],
      hasMore: true,
      loading: true
    }));
  };

  const setPage = (page: number) => {
    setState(prev => ({
      ...prev,
      page,
      loading: true
    }));
  };

  return {
    history: state.history,
    loading: state.loading,
    page: state.page,
    pageSize: state.pageSize,
    totalItems: state.totalItems,
    totalPages: state.totalPages,
    nameFilter: state.nameFilter,
    successFilter: state.successFilter,
    newDataAvailable: state.newDataAvailable,
    hasMore: state.hasMore,
    fetchCronJobHistory,
    applyNewData,
    setNameFilter,
    setSuccessFilter,
    loadMore,
    resetFilters,
    setPageSize,
    setPage,
    setLoading: (loading: boolean) => setState(prev => ({ ...prev, loading }))
  };
};
